import cv2
import time

def test_frame_limit():
    """Test the 100 frame limit functionality"""
    print("=== TESTING 100 FRAME LIMIT ===\n")
    
    # Initialize camera
    cap = cv2.VideoCapture(1)  # Use camera 1 (known working)
    if not cap.isOpened():
        cap = cv2.VideoCapture(0)  # Fallback to camera 0
    
    if not cap.isOpened():
        print("❌ Could not open camera")
        return
    
    print("✅ Camera opened successfully")
    print("Testing frame limit functionality...")
    print("This will capture exactly 100 frames and then stop automatically")
    print("Press 'q' to exit early\n")
    
    frame_count = 0
    max_frames = 100
    start_time = time.time()
    
    while frame_count < max_frames:
        ret, frame = cap.read()
        if not ret:
            print("❌ Failed to read frame")
            break
        
        frame_count += 1
        
        # Add frame counter and remaining frames info
        cv2.putText(frame, f"Frame: {frame_count}/{max_frames}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, "Press 'q' to exit early", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Show remaining frames
        remaining = max_frames - frame_count
        cv2.putText(frame, f"Remaining: {remaining}", (10, 90), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # Progress bar
        progress = int((frame_count / max_frames) * 400)  # 400 pixel wide progress bar
        cv2.rectangle(frame, (10, 120), (410, 140), (100, 100, 100), 2)  # Border
        cv2.rectangle(frame, (12, 122), (12 + progress, 138), (0, 255, 0), -1)  # Progress
        
        cv2.imshow('Frame Limit Test', frame)
        
        # Check for exit key
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            print(f"Exited manually at frame {frame_count}")
            break
        
        # Print progress every 10 frames
        if frame_count % 10 == 0:
            elapsed = time.time() - start_time
            fps = frame_count / elapsed
            print(f"Frame {frame_count}/{max_frames} - FPS: {fps:.1f}")
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_fps = frame_count / total_time
    
    cap.release()
    cv2.destroyAllWindows()
    
    print(f"\n=== TEST RESULTS ===")
    print(f"Total frames captured: {frame_count}")
    print(f"Maximum frames limit: {max_frames}")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Average FPS: {avg_fps:.2f}")
    
    if frame_count >= max_frames:
        print("✅ Frame limit working correctly - stopped at 100 frames")
    else:
        print(f"⚠️  Stopped early at {frame_count} frames")

if __name__ == "__main__":
    test_frame_limit()
