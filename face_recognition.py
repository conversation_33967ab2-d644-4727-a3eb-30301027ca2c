from tkinter import *
from tkinter import messagebox as msgbox
from PIL import Image, ImageTk
from datetime import datetime
import cv2
import mysql.connector
import os
import time

class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.last_attendance_time = {}  # Track last attendance time for each student ID
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        # Title label
        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)

        # First image
        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        f_lbl_left = Label(self.root, image=self.photo_left)
        f_lbl_left.place(x=0, y=55, width=650, height=700)


        # Second image
        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        f_lbl_right = Label(self.root, image=self.photo_right)
        f_lbl_right.place(x=650, y=55, width=950, height=700)

        # Face Recognition Button
        Button(self.root, text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",
               font=("times new roman", 15, "bold"), bg="darkgreen", fg="white").place(x=1012, y=680, width=220, height=41)
#===============Attendance=================
    def mark_attendance(self, n, r, d, id):
        now = datetime.now()
        current_date = now.strftime("%d/%m/%Y")
        current_time = now.strftime("%H:%M:%S")
        current_timestamp = time.time()

        # Check if we recently marked attendance for this person (prevent rapid duplicates)
        if id in self.last_attendance_time:
            time_diff = current_timestamp - self.last_attendance_time[id]
            if time_diff < 10:  # Wait at least 10 seconds between markings
                print(f"Attendance for {n} (ID: {id}) was recently marked. Waiting...")
                return

        # Check if attendance file exists, if not create with header
        if not os.path.exists("attendance.csv"):
            with open("attendance.csv", "w", newline="") as f:
                f.write("Name,Roll,Department,Student_ID,Time,Date,Status\n")

        # Read existing attendance records to check if already marked today
        try:
            with open("attendance.csv", "r") as f:
                lines = f.readlines()
                for line in lines[1:]:  # Skip header
                    if line.strip():  # Skip empty lines
                        parts = line.strip().split(",")
                        if len(parts) >= 6:
                            # Check if this person already marked attendance today
                            record_date = parts[5]  # Date column
                            record_id = parts[3]    # Student_ID column
                            if record_date == current_date and record_id == id:
                                print(f"✓ Attendance already marked for {n} (ID: {id}) today ({current_date})")
                                return  # Don't mark again
        except FileNotFoundError:
            pass  # File doesn't exist yet, will be created

        # Mark new attendance
        with open("attendance.csv", "a", newline="") as f:
            f.write(f"{n},{r},{d},{id},{current_time},{current_date},Present\n")
            self.last_attendance_time[id] = current_timestamp  # Update last marking time
            print(f"✓ NEW ATTENDANCE: {n} (ID: {id}) marked present at {current_time} on {current_date}")

#==========Face Recognition Function==========
    def face_recog(self):
        def draw_boundary(img, classifier, scaleFactor, minNeighbors, color, text, clf):
            gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            # Use more sensitive parameters for better face detection
            features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors, minSize=(30, 30))
            cord = []

            # Debug: Show number of faces detected
            if len(features) > 0:
                print(f"Detected {len(features)} face(s)")

            for (x, y, w, h) in features:
                id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                confidence = int(100 * (1 - predict / 300))

                if confidence > 77:
                    # Known face - green rectangle
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 0), 3)

                    try:
                        # Connect to database
                        conn = mysql.connector.connect(host="localhost", username="root", password="Admin@1402", database="face_recognizer")
                        my_cursor = conn.cursor()

                        # Get student details
                        my_cursor.execute("select Name from student where Student_id="+str(id))
                        n_result = my_cursor.fetchone()
                        n = "+".join(n_result) if n_result else "Unknown"

                        my_cursor.execute("select Roll from student where Student_id="+str(id))
                        r_result = my_cursor.fetchone()
                        r = "+".join(r_result) if r_result else "Unknown"

                        my_cursor.execute("select Dep from student where Student_id="+str(id))
                        d_result = my_cursor.fetchone()
                        d = "+".join(d_result) if d_result else "Unknown"

                        my_cursor.execute("select Student_id from student where Student_id="+str(id))
                        id_result = my_cursor.fetchone()
                        student_id = "+".join(id_result) if id_result else str(id)

                        # Close database connection
                        conn.close()

                        # Display student information
                        cv2.putText(img, f"ID: {student_id}", (x, y-75), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        cv2.putText(img, f"Name: {n}", (x, y-55), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        cv2.putText(img, f"Roll: {r}", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        cv2.putText(img, f"Dept: {d}", (x, y-5), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)

                        # Mark attendance
                        self.mark_attendance(n, r, d, student_id)

                    except Exception as e:
                        print(f"Database error: {e}")
                        cv2.putText(img, "DB Error", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)
                else:
                    # Unknown face - red rectangle
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                    cv2.putText(img, "Unknown", (x, y-10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)

                cord = [x, y, w, h]

            return cord
        def recognize(img, clf, faceCascade):
            # Use better parameters for face detection: scaleFactor=1.3, minNeighbors=5 (same as student.py)
            cord = draw_boundary(img, faceCascade, 1.3, 5, (255, 255, 255), "Face", clf)
            return img

        # Load face cascade classifier
        faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
        if faceCascade.empty():
            msgbox.showerror("Error", "Could not load haarcascade_frontalface_default.xml file")
            return

        # Load trained classifier
        clf = cv2.face.LBPHFaceRecognizer_create()
        try:
            clf.read("classifier.xml")
        except Exception as e:
            msgbox.showerror("Error", f"Could not load classifier.xml file: {str(e)}")
            return

        # Initialize camera - try different camera indices and backends (same as student.py)
        video_capture = None
        camera_indices = [1, 0, 2]  # Try camera 1 first (known working), then others
        backends = [cv2.CAP_DSHOW, cv2.CAP_ANY]  # Use DSHOW first for Windows

        for cam_idx in camera_indices:
            for backend in backends:
                try:
                    print(f"Trying camera {cam_idx} with backend {backend}")
                    video_capture = cv2.VideoCapture(cam_idx, backend)
                    if video_capture.isOpened():
                        # Set properties before testing
                        video_capture.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                        video_capture.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                        video_capture.set(cv2.CAP_PROP_FPS, 30)
                        video_capture.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                        # Test if we can read a frame
                        ret, test_frame = video_capture.read()
                        if ret and test_frame is not None:
                            print(f"✓ Camera {cam_idx} working with backend {backend}")
                            print(f"Frame shape: {test_frame.shape}")
                            break
                        else:
                            print(f"✗ Camera {cam_idx} opened but cannot read frames")
                            video_capture.release()
                            video_capture = None
                    else:
                        print(f"✗ Cannot open camera {cam_idx} with backend {backend}")
                except Exception as e:
                    print(f"✗ Error with camera {cam_idx}, backend {backend}: {e}")
                    if video_capture:
                        video_capture.release()
                    video_capture = None
                    continue
            if video_capture is not None:
                break

        # Check if camera opened successfully
        if video_capture is None or not video_capture.isOpened():
            msgbox.showerror("Error", "Could not open any camera. Please check if camera is connected and not being used by another application.")
            return

        print("Camera opened successfully. Press 'Enter' or 'q' to exit.")
        print("Face detection parameters: scaleFactor=1.3, minNeighbors=5, minSize=(30,30)")
        print("Maximum frames: 100")

        frame_count = 0
        max_frames = 100

        while frame_count < max_frames:
            ret, img = video_capture.read()
            if not ret:
                print("Failed to read frame from camera")
                msgbox.showerror("Error", "Failed to read from camera")
                break

            frame_count += 1

            # Process the frame for face recognition
            img = recognize(img, clf, faceCascade)

            # Add frame counter and remaining frames info
            cv2.putText(img, f"Frame: {frame_count}/{max_frames}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(img, "Press Enter or Q to exit", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Show remaining frames
            remaining = max_frames - frame_count
            cv2.putText(img, f"Remaining: {remaining}", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

            # Display the frame
            cv2.imshow("Face Recognition System - Press Enter to Exit", img)

            # Check for exit key (Enter = 13)
            key = cv2.waitKey(1) & 0xFF
            if key == 13 or key == ord('q'):  # Enter or 'q' to exit
                print(f"Exited manually at frame {frame_count}")
                break

        # Check if we reached the maximum frames
        if frame_count >= max_frames:
            print(f"Reached maximum frames limit: {max_frames}")
            msgbox.showinfo("Complete", f"Face recognition completed after {max_frames} frames.")

        video_capture.release()
        cv2.destroyAllWindows()
        print("Camera closed successfully.")
    
       
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()          