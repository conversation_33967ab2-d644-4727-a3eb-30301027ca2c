from tkinter import *
from tkinter import messagebox as msgbox 
from PIL import Image, ImageTk
from datetime import datetime
import cv2
import mysql.connector
class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        # Title label
        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)

        # First image
        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        f_lbl_left = Label(self.root, image=self.photo_left)
        f_lbl_left.place(x=0, y=55, width=650, height=700)


        # Second image
        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        f_lbl_right = Label(self.root, image=self.photo_right)
        f_lbl_right.place(x=650, y=55, width=950, height=700)

        # Face Recognition Button
        Button(self.root, text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",
               font=("times new roman", 15, "bold"), bg="darkgreen", fg="white").place(x=1012, y=680, width=220, height=41)
#===============Attendance=================
    def mark_attendance(self, n, r, d, id):
        with open("attendance.csv", "a+", newline="\n") as f:
            myDataList = f.readlines()
            name_list = []
            for line in myDataList:
                entry = line.split((", "))
                name_list.append(entry[0])
            if (n not in name_list) and (r not in name_list) and (d not in name_list) and (id not in name_list):
                now = datetime.now()
                d1 = now.strftime("%d/%m/%Y")
                dtString = now.strftime("%H:%M:%S")
                f.writelines(f"\n{n},{r},{d},{id},{dtString},{d1},Present")

#==========Face Recognition Function==========
    def face_recog(self):
        def draw_boundary(img, classifier, scaleFactor, minNeighbors,color,text, clf):
            gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors)
            cord =[]
            for (x, y, w, h) in features:
                cv2.rectangle(img, (x, y), (x+w, y+h),(0, 255, 0),3)
                id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                confidence = int(100 * (1 - predict / 300))
                conn = mysql.connector.connect(host="localhost", username="root", password="Admin@1402", database="face_recognizer")
                my_cursor = conn.cursor()
               
                my_cursor.execute("select Name from student where Student_id="+str(id))
                n = my_cursor.fetchone()
                n = "+".join(n)

                my_cursor.execute("select Roll from student where Student_id="+str(id))
                r = my_cursor.fetchone()
                r = "+".join(r)
                
                my_cursor.execute("select Dep from student where Student_id="+str(id))
                d = my_cursor.fetchone()
                d = "+".join(d)
                
                my_cursor.execute ("select Student_id from student where Student_id="+str(id))
                id = my_cursor.fetchone()
                id = "+".join(id) 
                
                if confidence > 77:
                    cv2.putText(img, f"Student ID: {id}", (x, y-75), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                    cv2.putText(img, f"Name: {n}", (x, y-55), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                    cv2.putText(img, f"Roll: {r}", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                    cv2.putText(img, f"Department: {d}", (x, y-5), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                    self.mark_attendance(n, r, d, id)
                else:
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                cord = [x,y,w,h]
                
            return cord
        def recognize(img, clf, faceCascade):
            cord = draw_boundary(img, faceCascade, 1.1, 10, (255, 255, 255), "Face", clf)
            return img

        # Load face cascade classifier
        faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
        if faceCascade.empty():
            msgbox.showerror("Error", "Could not load haarcascade_frontalface_default.xml file")
            return

        # Load trained classifier
        clf = cv2.face.LBPHFaceRecognizer_create()
        try:
            clf.read("classifier.xml")
        except Exception as e:
            msgbox.showerror("Error", f"Could not load classifier.xml file: {str(e)}")
            return

        # Initialize camera
        video_capture = cv2.VideoCapture(0)
        if not video_capture.isOpened():
            msgbox.showerror("Error", "Could not open camera. Please check if camera is connected and not being used by another application.")
            return

        while True:
            ret, img = video_capture.read()
            if not ret:
                msgbox.showerror("Error", "Failed to read from camera")
                break
            img = recognize(img, clf, faceCascade)
            cv2.imshow("Welcome To Face Recognition", img)
            if cv2.waitKey(1) == 13:  # Enter key to exit
                break
        video_capture.release()
        cv2.destroyAllWindows()
    
       
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()          