import cv2
import sys

def test_camera():
    """Simple camera test to verify camera functionality"""
    print("Testing camera...")
    
    # Try to open camera
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("ERROR: Could not open camera")
        print("Possible solutions:")
        print("1. Check if camera is connected")
        print("2. Close other applications using camera")
        print("3. Try different camera index (1, 2, etc.)")
        return False
    
    print("Camera opened successfully!")
    print("Camera properties:")
    print(f"Width: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}")
    print(f"Height: {cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")
    print(f"FPS: {cap.get(cv2.CAP_PROP_FPS)}")
    
    print("\nPress 'q' to quit the camera test")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        
        if not ret:
            print("ERROR: Failed to read frame from camera")
            break
        
        frame_count += 1
        
        # Add frame counter to image
        cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(frame, "Press 'q' to quit", (10, 70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # Display the frame
        cv2.imshow('Camera Test', frame)
        
        # Check for quit key
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
    print(f"Camera test completed. Total frames: {frame_count}")
    return True

if __name__ == "__main__":
    test_camera()
