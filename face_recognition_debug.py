import cv2
import numpy as np
import mysql.connector
import os

def test_camera_detection():
    """Test camera and face detection capabilities"""
    print("=== FACE RECOGNITION DIAGNOSTIC TEST ===\n")
    
    # 1. Test camera access
    print("1. Testing camera access...")
    camera_indices = [1, 0, 2]
    backends = [cv2.CAP_DSHOW, cv2.CAP_ANY]
    working_camera = None
    
    for cam_idx in camera_indices:
        for backend in backends:
            try:
                print(f"   Trying camera {cam_idx} with backend {backend}")
                cap = cv2.VideoCapture(cam_idx, backend)
                if cap.isOpened():
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        print(f"   ✓ Camera {cam_idx} working with backend {backend}")
                        working_camera = (cam_idx, backend)
                        cap.release()
                        break
                    else:
                        cap.release()
                else:
                    print(f"   ✗ Cannot open camera {cam_idx}")
            except Exception as e:
                print(f"   ✗ Error: {e}")
        if working_camera:
            break
    
    if not working_camera:
        print("   ❌ NO WORKING CAMERA FOUND!")
        return False
    
    print(f"   ✅ Working camera found: Index {working_camera[0]}, Backend {working_camera[1]}\n")
    
    # 2. Test face cascade classifier
    print("2. Testing face cascade classifier...")
    cascade_path = "haarcascade_frontalface_default.xml"
    if not os.path.exists(cascade_path):
        print(f"   ❌ File not found: {cascade_path}")
        return False
    
    face_cascade = cv2.CascadeClassifier(cascade_path)
    if face_cascade.empty():
        print(f"   ❌ Failed to load cascade classifier")
        return False
    
    print(f"   ✅ Face cascade loaded successfully\n")
    
    # 3. Test trained classifier
    print("3. Testing trained face recognizer...")
    classifier_path = "classifier.xml"
    if not os.path.exists(classifier_path):
        print(f"   ❌ File not found: {classifier_path}")
        return False
    
    try:
        clf = cv2.face.LBPHFaceRecognizer_create()
        clf.read(classifier_path)
        print(f"   ✅ Face recognizer loaded successfully\n")
    except Exception as e:
        print(f"   ❌ Failed to load face recognizer: {e}")
        return False
    
    # 4. Test database connection
    print("4. Testing database connection...")
    try:
        conn = mysql.connector.connect(
            host="localhost", 
            username="root", 
            password="Admin@1402", 
            database="face_recognizer"
        )
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM student")
        count = cursor.fetchone()[0]
        print(f"   ✅ Database connected. Found {count} students in database")
        conn.close()
    except Exception as e:
        print(f"   ❌ Database connection failed: {e}")
        print("   Note: Face recognition will still work, but student info won't be displayed")
    
    print()
    
    # 5. Live face detection test
    print("5. Starting live face detection test...")
    print("   Press 'q' to quit, 's' to take screenshot")
    
    cap = cv2.VideoCapture(working_camera[0], working_camera[1])
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    frame_count = 0
    face_detected_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("   ❌ Failed to read frame")
            break
        
        frame_count += 1
        
        # Convert to grayscale for face detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Detect faces with same parameters as face_recognition.py
        faces = face_cascade.detectMultiScale(gray, 1.3, 5, minSize=(30, 30))
        
        # Draw rectangles around faces
        for (x, y, w, h) in faces:
            cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
            cv2.putText(frame, "Face Detected", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            face_detected_count += 1
        
        # Add info text
        cv2.putText(frame, f"Frame: {frame_count}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Faces detected: {len(faces)}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, "Press 'q' to quit", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow('Face Detection Test', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            cv2.imwrite(f"debug_screenshot_{frame_count}.jpg", frame)
            print(f"   Screenshot saved: debug_screenshot_{frame_count}.jpg")
    
    cap.release()
    cv2.destroyAllWindows()
    
    print(f"\n   Test completed:")
    print(f"   - Total frames: {frame_count}")
    print(f"   - Faces detected: {face_detected_count}")
    
    if face_detected_count > 0:
        print(f"   ✅ Face detection is working!")
    else:
        print(f"   ⚠️  No faces detected. Try:")
        print(f"      - Better lighting")
        print(f"      - Position face directly in front of camera")
        print(f"      - Check if face is large enough (minimum 30x30 pixels)")
    
    return True

if __name__ == "__main__":
    test_camera_detection()
