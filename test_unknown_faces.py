import os
import csv
from datetime import datetime
import time

def test_unknown_face_logic():
    """Test that unknown faces are NOT added to attendance"""
    print("=== TESTING UNKNOWN FACE LOGIC ===\n")
    
    # Create a backup of current attendance file
    if os.path.exists("attendance.csv"):
        os.rename("attendance.csv", "attendance_backup.csv")
        print("Backed up existing attendance.csv")
    
    # Simulate the Face_Recognition class
    class TestFaceRecognition:
        def __init__(self):
            self.last_attendance_time = {}
        
        def mark_attendance(self, n, r, d, id):
            """Only mark attendance for valid students (not Unknown)"""
            now = datetime.now()
            current_date = now.strftime("%d/%m/%Y")
            current_time = now.strftime("%H:%M:%S")
            current_timestamp = time.time()
            
            # Check if we recently marked attendance for this person
            if id in self.last_attendance_time:
                time_diff = current_timestamp - self.last_attendance_time[id]
                if time_diff < 10:
                    print(f"⏳ Attendance for {n} (ID: {id}) was recently marked. Waiting...")
                    return
            
            # Check if attendance file exists, if not create with header
            if not os.path.exists("attendance.csv"):
                with open("attendance.csv", "w", newline="") as f:
                    f.write("Name,Roll,Department,Student_ID,Time,Date,Status\n")
            
            # Read existing attendance records
            try:
                with open("attendance.csv", "r") as f:
                    lines = f.readlines()
                    for line in lines[1:]:  # Skip header
                        if line.strip():
                            parts = line.strip().split(",")
                            if len(parts) >= 6:
                                record_date = parts[5]
                                record_id = parts[3]
                                if record_date == current_date and record_id == id:
                                    print(f"✓ Attendance already marked for {n} (ID: {id}) today")
                                    return
            except FileNotFoundError:
                pass
            
            # Mark new attendance
            with open("attendance.csv", "a", newline="") as f:
                f.write(f"{n},{r},{d},{id},{current_time},{current_date},Present\n")
                self.last_attendance_time[id] = current_timestamp
                print(f"✅ NEW ATTENDANCE: {n} (ID: {id}) marked present")
        
        def simulate_face_detection(self, confidence, student_exists_in_db, student_data):
            """Simulate the face detection logic"""
            print(f"\n--- Simulating face detection ---")
            print(f"Confidence: {confidence}%")
            print(f"Student exists in DB: {student_exists_in_db}")
            
            if confidence > 77:
                if student_exists_in_db and student_data:
                    # Known student - should mark attendance
                    n, r, d, student_id = student_data
                    print(f"✅ Known student detected: {n}")
                    print("🟢 GREEN RECTANGLE - Marking attendance")
                    self.mark_attendance(n, r, d, student_id)
                else:
                    # Student not found in database
                    print("❌ Student not found in database")
                    print("🔴 RED RECTANGLE - NOT marking attendance")
                    print("Display: 'Unknown Student'")
            else:
                # Low confidence face
                print("❌ Low confidence face")
                print("🔴 RED RECTANGLE - NOT marking attendance")
                print("Display: 'Unknown Face'")
    
    # Test scenarios
    face_rec = TestFaceRecognition()
    
    print("1. Testing known student (high confidence + in database)...")
    face_rec.simulate_face_detection(85, True, ("Alice Johnson", "CS101", "Computer Science", "10"))
    
    print("\n2. Testing unknown face (high confidence but NOT in database)...")
    face_rec.simulate_face_detection(85, False, None)
    
    print("\n3. Testing low confidence face...")
    face_rec.simulate_face_detection(65, True, ("Bob Smith", "CS102", "Computer Science", "11"))
    
    print("\n4. Testing another known student...")
    face_rec.simulate_face_detection(90, True, ("Carol Davis", "CS103", "Computer Science", "12"))
    
    print("\n5. Testing duplicate detection of same student...")
    face_rec.simulate_face_detection(88, True, ("Alice Johnson", "CS101", "Computer Science", "10"))
    
    # Check final attendance file
    print("\n=== FINAL ATTENDANCE RECORDS ===")
    if os.path.exists("attendance.csv"):
        with open("attendance.csv", "r") as f:
            content = f.read()
            print(content)
            
        # Count entries
        lines = content.strip().split('\n')
        total_entries = len(lines) - 1  # Exclude header
        unknown_entries = sum(1 for line in lines[1:] if 'Unknown' in line)
        
        print(f"Total attendance entries: {total_entries}")
        print(f"Unknown entries: {unknown_entries}")
        
        if unknown_entries == 0:
            print("✅ SUCCESS: No unknown faces in attendance!")
        else:
            print("❌ FAIL: Unknown faces found in attendance!")
    else:
        print("No attendance file created")
    
    # Restore backup
    if os.path.exists("attendance_backup.csv"):
        if os.path.exists("attendance.csv"):
            os.remove("attendance.csv")
        os.rename("attendance_backup.csv", "attendance.csv")
        print("\nRestored original attendance.csv")
    
    print("\n=== TEST COMPLETED ===")

if __name__ == "__main__":
    test_unknown_face_logic()
