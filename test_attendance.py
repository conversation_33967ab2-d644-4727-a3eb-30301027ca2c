import os
import csv
from datetime import datetime
import time

def test_attendance_system():
    """Test the attendance marking system"""
    print("=== TESTING ATTENDANCE SYSTEM ===\n")
    
    # Remove existing attendance file for clean test
    if os.path.exists("attendance.csv"):
        os.remove("attendance.csv")
        print("Removed existing attendance.csv for clean test")
    
    # Simulate the Face_Recognition class attendance marking
    class TestFaceRecognition:
        def __init__(self):
            self.last_attendance_time = {}
        
        def mark_attendance(self, n, r, d, id):
            now = datetime.now()
            current_date = now.strftime("%d/%m/%Y")
            current_time = now.strftime("%H:%M:%S")
            current_timestamp = time.time()
            
            # Check if we recently marked attendance for this person (prevent rapid duplicates)
            if id in self.last_attendance_time:
                time_diff = current_timestamp - self.last_attendance_time[id]
                if time_diff < 10:  # Wait at least 10 seconds between markings
                    print(f"⏳ Attendance for {n} (ID: {id}) was recently marked. Waiting...")
                    return
            
            # Check if attendance file exists, if not create with header
            if not os.path.exists("attendance.csv"):
                with open("attendance.csv", "w", newline="") as f:
                    f.write("Name,Roll,Department,Student_ID,Time,Date,Status\n")
            
            # Read existing attendance records to check if already marked today
            try:
                with open("attendance.csv", "r") as f:
                    lines = f.readlines()
                    for line in lines[1:]:  # Skip header
                        if line.strip():  # Skip empty lines
                            parts = line.strip().split(",")
                            if len(parts) >= 6:
                                # Check if this person already marked attendance today
                                record_date = parts[5]  # Date column
                                record_id = parts[3]    # Student_ID column
                                if record_date == current_date and record_id == id:
                                    print(f"✓ Attendance already marked for {n} (ID: {id}) today ({current_date})")
                                    return  # Don't mark again
            except FileNotFoundError:
                pass  # File doesn't exist yet, will be created
            
            # Mark new attendance
            with open("attendance.csv", "a", newline="") as f:
                f.write(f"{n},{r},{d},{id},{current_time},{current_date},Present\n")
                self.last_attendance_time[id] = current_timestamp  # Update last marking time
                print(f"✅ NEW ATTENDANCE: {n} (ID: {id}) marked present at {current_time} on {current_date}")
    
    # Test the system
    face_rec = TestFaceRecognition()
    
    print("1. Testing first attendance marking...")
    face_rec.mark_attendance("John Doe", "CS001", "Computer Science", "1")
    
    print("\n2. Testing duplicate marking (should be prevented)...")
    face_rec.mark_attendance("John Doe", "CS001", "Computer Science", "1")
    
    print("\n3. Testing different student...")
    face_rec.mark_attendance("Jane Smith", "CS002", "Computer Science", "2")
    
    print("\n4. Testing rapid duplicate (should be prevented)...")
    face_rec.mark_attendance("Jane Smith", "CS002", "Computer Science", "2")
    
    print("\n5. Waiting 11 seconds and trying again...")
    print("   (In real system, this would be prevented by daily check)")
    time.sleep(11)
    face_rec.mark_attendance("John Doe", "CS001", "Computer Science", "1")
    
    # Display final attendance file
    print("\n=== FINAL ATTENDANCE RECORDS ===")
    if os.path.exists("attendance.csv"):
        with open("attendance.csv", "r") as f:
            print(f.read())
    else:
        print("No attendance file created")
    
    print("=== TEST COMPLETED ===")

if __name__ == "__main__":
    test_attendance_system()
